{"timestamp": "2025-06-14T21:45:52.059Z", "componentResults": {"original": {"files": [{"name": "audience-header.tsx", "size": 508, "sizeKB": "0.50", "optimized": true}, {"name": "audience-interests.tsx", "size": 3664, "sizeKB": "3.58", "optimized": true}, {"name": "audience-overview.tsx", "size": 2601, "sizeKB": "2.54", "optimized": true}, {"name": "audience-segments.tsx", "size": 4751, "sizeKB": "4.64", "optimized": true}, {"name": "audience-growth.tsx", "size": 5161, "sizeKB": "5.04", "optimized": true}, {"name": "audience-activity.tsx", "size": 3791, "sizeKB": "3.70", "optimized": true}, {"name": "audience-demographics.tsx", "size": 5032, "sizeKB": "4.91", "optimized": true}, {"name": "audience-growth.tsx", "size": 5284, "sizeKB": "5.16", "optimized": true}, {"name": "audience-activity.tsx", "size": 4638, "sizeKB": "4.53", "optimized": true}, {"name": "audience-demographics.tsx", "size": 4574, "sizeKB": "4.47", "optimized": true}], "total": 40004}, "optimized": {"files": [{"name": "optimized-audience-growth.tsx", "size": 5649, "sizeKB": "5.52", "optimized": true}, {"name": "optimized-audience-activity.tsx", "size": 5015, "sizeKB": "4.90", "optimized": true}, {"name": "optimized-audience-demographics.tsx", "size": 5155, "sizeKB": "5.03", "optimized": true}, {"name": "optimized-audience-interests.tsx", "size": 4972, "sizeKB": "4.86", "optimized": true}, {"name": "optimized-audience-segments.tsx", "size": 6371, "sizeKB": "6.22", "optimized": true}], "total": 27162}, "improvements": {"totalComponents": 15, "optimizedComponents": 5, "optimizationPercentage": 100, "newOptimizedComponents": 5, "averageOriginalSize": 4000.4, "averageOptimizedSize": 5432.4}}, "bundleResults": {"hasOptimizedCharts": true, "hasOptimizedPage": true, "lazyImports": 5, "suspenseUsage": 5, "memoUsage": 18, "optimizationFeatures": {"canvasCharts": true, "lazyLoading": true, "suspenseBoundaries": true, "memoization": true}}, "features": {"firefoxOptimizations": true, "intersectionObserver": true, "performanceMonitoring": true, "virtualizedLists": true, "optimizedCharts": true}, "config": {"routes": {"original": "/dashboard/audience", "optimized": "/dashboard/audience/optimized"}, "expectedImprovements": {"loadTime": 25, "memoryUsage": 20, "bundleSize": 15, "componentOptimization": 90}}}