"use client"
import { useState, useEffect, memo, useCallback, useMemo } from "react"
// Icons are now imported in the sub-components
import { toast } from "sonner"

import { useIsMobile } from "@/hooks/use-mobile"
import { Button } from "@/components/ui/button"
import { QRCodeModal } from "./qr-code-modal"
import { CreateLinkWizard } from "./create-link-wizard"
import { AffiliateLinkToolbar } from "./components/AffiliateLinkToolbar"
import { AffiliateLinkTableView } from "./components/AffiliateLinkTableView"
import { AffiliateLinkMobileView } from "./components/AffiliateLinkMobileView"

// Types
interface AffiliateLink {
  id: string
  campaign: string
  campaignLogo: string
  link: string
  clicks: number
  clicksChange: number
  earnings: number
  earningsChange: number
  status: "active" | "paused" | "completed"
  performance: { date: string; clicks: number }[]
}

// Sample data
const sampleAffiliateLinks: AffiliateLink[] = [
  {
    id: "1",
    campaign: "Summer Fashion Collection",
    campaignLogo: "/placeholder.svg?height=40&width=40",
    link: "https://example.com/ref/user123/summer-fashion",
    clicks: 1245,
    clicksChange: 12,
    earnings: 623.45,
    earningsChange: 8,
    status: "active",
    performance: Array.from({ length: 30 }, (_, i) => ({
      date: `2023-06-${i + 1}`,
      clicks: Math.floor(Math.random() * 100) + 20,
    })),
  },
  {
    id: "2",
    campaign: "Tech Gadgets Promo",
    campaignLogo: "/placeholder.svg?height=40&width=40",
    link: "https://example.com/ref/user123/tech-gadgets",
    clicks: 876,
    clicksChange: -5,
    earnings: 438.2,
    earningsChange: -3,
    status: "active",
    performance: Array.from({ length: 30 }, (_, i) => ({
      date: `2023-06-${i + 1}`,
      clicks: Math.floor(Math.random() * 80) + 10,
    })),
  },
  {
    id: "3",
    campaign: "Home Decor Sale",
    campaignLogo: "/placeholder.svg?height=40&width=40",
    link: "https://example.com/ref/user123/home-decor",
    clicks: 532,
    clicksChange: 3,
    earnings: 266.0,
    earningsChange: 5,
    status: "paused",
    performance: Array.from({ length: 30 }, (_, i) => ({
      date: `2023-06-${i + 1}`,
      clicks: Math.floor(Math.random() * 60) + 5,
    })),
  },
  {
    id: "4",
    campaign: "Fitness Equipment",
    campaignLogo: "/placeholder.svg?height=40&width=40",
    link: "https://example.com/ref/user123/fitness",
    clicks: 1089,
    clicksChange: 15,
    earnings: 544.5,
    earningsChange: 18,
    status: "active",
    performance: Array.from({ length: 30 }, (_, i) => ({
      date: `2023-06-${i + 1}`,
      clicks: Math.floor(Math.random() * 90) + 30,
    })),
  },
  {
    id: "5",
    campaign: "Beauty Products Bundle",
    campaignLogo: "/placeholder.svg?height=40&width=40",
    link: "https://example.com/ref/user123/beauty",
    clicks: 765,
    clicksChange: 7,
    earnings: 382.5,
    earningsChange: 9,
    status: "active",
    performance: Array.from({ length: 30 }, (_, i) => ({
      date: `2023-06-${i + 1}`,
      clicks: Math.floor(Math.random() * 70) + 15,
    })),
  },
  {
    id: "6",
    campaign: "Travel Package Deals",
    campaignLogo: "/placeholder.svg?height=40&width=40",
    link: "https://example.com/ref/user123/travel",
    clicks: 421,
    clicksChange: -8,
    earnings: 210.5,
    earningsChange: -10,
    status: "completed",
    performance: Array.from({ length: 30 }, (_, i) => ({
      date: `2023-06-${i + 1}`,
      clicks: Math.floor(Math.random() * 50) + 5,
    })),
  },
  {
    id: "7",
    campaign: "Online Course Promotion",
    campaignLogo: "/placeholder.svg?height=40&width=40",
    link: "https://example.com/ref/user123/courses",
    clicks: 932,
    clicksChange: 20,
    earnings: 466.0,
    earningsChange: 22,
    status: "active",
    performance: Array.from({ length: 30 }, (_, i) => ({
      date: `2023-06-${i + 1}`,
      clicks: Math.floor(Math.random() * 85) + 25,
    })),
  },
  {
    id: "8",
    campaign: "Food Delivery Service",
    campaignLogo: "/placeholder.svg?height=40&width=40",
    link: "https://example.com/ref/user123/food-delivery",
    clicks: 654,
    clicksChange: 5,
    earnings: 327.0,
    earningsChange: 4,
    status: "paused",
    performance: Array.from({ length: 30 }, (_, i) => ({
      date: `2023-06-${i + 1}`,
      clicks: Math.floor(Math.random() * 65) + 10,
    })),
  },
]

export const AffiliateLinkManager = memo(function AffiliateLinkManager() {
  const isMobile = useIsMobile()
  const [links, setLinks] = useState<AffiliateLink[]>(sampleAffiliateLinks)
  const [selectedLinks, setSelectedLinks] = useState<string[]>([])
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [searchQuery, setSearchQuery] = useState<string>("")
  const [qrModalOpen, setQrModalOpen] = useState<boolean>(false)
  const [createModalOpen, setCreateModalOpen] = useState<boolean>(false)
  const [selectedLink, setSelectedLink] = useState<AffiliateLink | null>(null)

  // Memoize filtered links calculation
  const filteredLinks = useMemo(() => {
    let filtered = [...links]

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter((link) => link.status === statusFilter)
    }

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(
        (link) => link.campaign.toLowerCase().includes(query) || link.link.toLowerCase().includes(query),
      )
    }

    return filtered
  }, [links, statusFilter, searchQuery])

  // Memoized event handlers
  const handleSelectAll = useCallback((checked: boolean) => {
    if (checked) {
      setSelectedLinks(filteredLinks.map((link) => link.id))
    } else {
      setSelectedLinks([])
    }
  }, [filteredLinks])

  const handleSelectLink = useCallback((id: string, checked: boolean) => {
    if (checked) {
      setSelectedLinks((prev) => [...prev, id])
    } else {
      setSelectedLinks((prev) => prev.filter((linkId) => linkId !== id))
    }
  }, [])

  const handleCopyLink = useCallback((link: string) => {
    navigator.clipboard.writeText(link)
    toast.success("Link copied to clipboard")
  }, [])

  const handleGenerateQR = useCallback((link: AffiliateLink) => {
    setSelectedLink(link)
    setQrModalOpen(true)
  }, [])

  const handleViewAnalytics = useCallback((linkId: string) => {
    window.location.href = `/dashboard/affiliate-links/${linkId}/analytics`
  }, [])

  const handleStatusChange = useCallback((id: string, status: "active" | "paused" | "completed") => {
    setLinks((prev) => prev.map((link) => (link.id === id ? { ...link, status } : link)))
    toast.success(`Link status changed to ${status}`)
  }, [])

  const handleBulkStatusChange = useCallback((status: "active" | "paused" | "completed") => {
    setLinks((prev) => prev.map((link) => (selectedLinks.includes(link.id) ? { ...link, status } : link)))
    toast.success(`${selectedLinks.length} links updated to ${status}`)
    setSelectedLinks([])
  }, [selectedLinks])

  const handleBulkExport = useCallback(() => {
    const selectedData = links.filter((link) => selectedLinks.includes(link.id))
    const jsonData = JSON.stringify(selectedData, null, 2)
    const blob = new Blob([jsonData], { type: "application/json" })
    const url = URL.createObjectURL(blob)

    const a = document.createElement("a")
    a.href = url
    a.download = "affiliate-links-export.json"
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast.success(`${selectedLinks.length} links exported`)
  }, [links, selectedLinks])

  const handleBulkDelete = useCallback(() => {
    setLinks((prev) => prev.filter((link) => !selectedLinks.includes(link.id)))
    toast.success(`${selectedLinks.length} links deleted`)
    setSelectedLinks([])
  }, [selectedLinks])

  const handleLinkCreated = useCallback((newLink: AffiliateLink) => {
    setLinks((prev) => [newLink, ...prev])
    toast.success("New affiliate link created successfully")
  }, [])

  // Render mobile card view
  const renderMobileView = () => (
    <AffiliateLinkMobileView
      links={filteredLinks}
      selectedLinks={selectedLinks}
      onSelectLink={handleSelectLink}
      onCopyLink={handleCopyLink}
      onGenerateQR={handleGenerateQR}
      onViewAnalytics={handleViewAnalytics}
      onStatusChange={handleStatusChange}
    />
  )

  // Render desktop table view
  const renderDesktopView = () => (
    <AffiliateLinkTableView
      links={filteredLinks}
      selectedLinks={selectedLinks}
      onSelectAll={handleSelectAll}
      onSelectLink={handleSelectLink}
      onCopyLink={handleCopyLink}
      onGenerateQR={handleGenerateQR}
      onViewAnalytics={handleViewAnalytics}
      onStatusChange={handleStatusChange}
    />
  )

  return (
    <div className="space-y-4">
      {/* Toolbar */}
      <AffiliateLinkToolbar
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        statusFilter={statusFilter}
        onStatusFilterChange={setStatusFilter}
        selectedLinksCount={selectedLinks.length}
        onCreateLink={() => setCreateModalOpen(true)}
        onBulkStatusChange={handleBulkStatusChange}
        onBulkExport={handleBulkExport}
        onBulkDelete={handleBulkDelete}
      />

      {/* Selected count */}
      {selectedLinks.length > 0 && (
        <div className="text-sm text-muted-foreground">
          {selectedLinks.length} of {filteredLinks.length} links selected
        </div>
      )}

      {/* Links table/cards */}
      {filteredLinks.length === 0 ? (
        <div className="flex h-[300px] flex-col items-center justify-center rounded-md border border-dashed p-8 text-center">
          <div className="mx-auto flex max-w-[420px] flex-col items-center justify-center text-center">
            <h3 className="mt-4 text-lg font-semibold">No affiliate links found</h3>
            <p className="mb-4 mt-2 text-sm text-muted-foreground">
              {searchQuery || statusFilter !== "all"
                ? "Try adjusting your search or filter to find what you're looking for."
                : "You don't have any affiliate links yet. Create your first campaign to get started."}
            </p>
            <Button onClick={() => setCreateModalOpen(true)}>Create Affiliate Link</Button>
          </div>
        </div>
      ) : isMobile ? (
        renderMobileView()
      ) : (
        renderDesktopView()
      )}

      {/* QR Code Modal */}
      <QRCodeModal
        open={qrModalOpen}
        onOpenChange={setQrModalOpen}
        link={selectedLink?.link || ""}
        campaign={selectedLink?.campaign || ""}
      />

      {/* Create Link Wizard */}
      <CreateLinkWizard open={createModalOpen} onOpenChange={setCreateModalOpen} onLinkCreated={handleLinkCreated} />
    </div>
  )
})