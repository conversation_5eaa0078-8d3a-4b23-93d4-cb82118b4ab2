"use client"
import { memo } from "react"
import { LinkCard } from "./LinkCard"

interface AffiliateLink {
  id: string
  campaign: string
  campaignLogo: string
  link: string
  clicks: number
  clicksChange: number
  earnings: number
  earningsChange: number
  status: "active" | "paused" | "completed"
  performance: { date: string; clicks: number }[]
}

interface AffiliateLinkMobileViewProps {
  links: AffiliateLink[]
  selectedLinks: string[]
  onSelectLink: (id: string, checked: boolean) => void
  onCopyLink: (link: string) => void
  onGenerateQR: (link: AffiliateLink) => void
  onViewAnalytics: (linkId: string) => void
  onStatusChange: (id: string, status: "active" | "paused" | "completed") => void
}

export const AffiliateLinkMobileView = memo(function AffiliateLinkMobileView({
  links,
  selectedLinks,
  onSelectLink,
  onCopyLink,
  onGenerateQR,
  onViewAnalytics,
  onStatusChange,
}: AffiliateLinkMobileViewProps) {
  return (
    <div className="grid gap-4">
      {links.map((link) => (
        <LinkCard
          key={link.id}
          link={link}
          isSelected={selectedLinks.includes(link.id)}
          onSelect={onSelectLink}
          onCopyLink={onCopyLink}
          onGenerateQR={onGenerateQR}
          onViewAnalytics={onViewAnalytics}
          onStatusChange={onStatusChange}
        />
      ))}
    </div>
  )
})

AffiliateLinkMobileView.displayName = 'AffiliateLinkMobileView'
