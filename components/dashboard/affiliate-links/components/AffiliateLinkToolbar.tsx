"use client"
import { memo } from "react"
import {
  ChevronDownIcon,
  FilterIcon,
  PlusIcon,
  SearchIcon,
  PlayIcon,
  PauseIcon,
  CheckIcon,
  DownloadIcon,
  TrashIcon,
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface AffiliateLinkToolbarProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  statusFilter: string
  onStatusFilterChange: (status: string) => void
  selectedLinksCount: number
  onCreateLink: () => void
  onBulkStatusChange: (status: "active" | "paused" | "completed") => void
  onBulkExport: () => void
  onBulkDelete: () => void
}

export const AffiliateLinkToolbar = memo(function AffiliateLinkToolbar({
  searchQuery,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  selectedLinksCount,
  onCreateLink,
  onBulkStatusChange,
  onBulkExport,
  onBulkDelete,
}: AffiliateLinkToolbarProps) {
  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div className="flex items-center gap-2">
        <Button onClick={onCreateLink}>
          <PlusIcon className="mr-1 h-4 w-4" />
          Create Link
        </Button>
        <div className="relative w-full sm:w-72">
          <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search campaigns or links..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>
      </div>
      
      <div className="flex flex-wrap items-center gap-2">
        <Select value={statusFilter} onValueChange={onStatusFilterChange}>
          <SelectTrigger className="h-9 w-[180px] sm:w-[130px]">
            <div className="flex items-center gap-2">
              <FilterIcon className="h-4 w-4" />
              <SelectValue placeholder="Filter by status" />
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Status</SelectLabel>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="paused">Paused</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>

        {selectedLinksCount > 0 && (
          <>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="h-9">
                  Bulk Actions ({selectedLinksCount})
                  <ChevronDownIcon className="ml-1 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => onBulkStatusChange("active")}>
                  <PlayIcon className="mr-2 h-4 w-4" />
                  Activate Selected
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onBulkStatusChange("paused")}>
                  <PauseIcon className="mr-2 h-4 w-4" />
                  Pause Selected
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onBulkStatusChange("completed")}>
                  <CheckIcon className="mr-2 h-4 w-4" />
                  Complete Selected
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onBulkExport}>
                  <DownloadIcon className="mr-2 h-4 w-4" />
                  Export Selected
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onBulkDelete} className="text-destructive">
                  <TrashIcon className="mr-2 h-4 w-4" />
                  Delete Selected
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </>
        )}
      </div>
    </div>
  )
})

AffiliateLinkToolbar.displayName = 'AffiliateLinkToolbar'
