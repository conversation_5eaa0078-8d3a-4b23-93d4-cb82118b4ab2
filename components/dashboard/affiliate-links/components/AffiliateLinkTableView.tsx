"use client"
import { memo } from "react"
import {
  BarChart3Icon,
  CheckIcon,
  CopyIcon,
  MoreHorizontalIcon,
  PauseIcon,
  PlayIcon,
  QrCodeIcon,
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { StatusBadge } from "./StatusBadge"
import { ChangeIndicator } from "./ChangeIndicator"
import { LinkPerformanceChart } from "./LinkPerformanceChart"

interface AffiliateLink {
  id: string
  campaign: string
  campaignLogo: string
  link: string
  clicks: number
  clicksChange: number
  earnings: number
  earningsChange: number
  status: "active" | "paused" | "completed"
  performance: { date: string; clicks: number }[]
}

interface AffiliateLinkTableViewProps {
  links: AffiliateLink[]
  selectedLinks: string[]
  onSelectAll: (checked: boolean) => void
  onSelectLink: (id: string, checked: boolean) => void
  onCopyLink: (link: string) => void
  onGenerateQR: (link: AffiliateLink) => void
  onViewAnalytics: (linkId: string) => void
  onStatusChange: (id: string, status: "active" | "paused" | "completed") => void
}

export const AffiliateLinkTableView = memo(function AffiliateLinkTableView({
  links,
  selectedLinks,
  onSelectAll,
  onSelectLink,
  onCopyLink,
  onGenerateQR,
  onViewAnalytics,
  onStatusChange,
}: AffiliateLinkTableViewProps) {
  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[40px]">
              <Checkbox
                checked={links.length > 0 && selectedLinks.length === links.length}
                onCheckedChange={onSelectAll}
                aria-label="Select all"
              />
            </TableHead>
            <TableHead>Campaign</TableHead>
            <TableHead>Link</TableHead>
            <TableHead>Clicks</TableHead>
            <TableHead>Earnings</TableHead>
            <TableHead>Performance</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {links.map((link) => (
            <TableRow key={link.id}>
              <TableCell>
                <Checkbox
                  checked={selectedLinks.includes(link.id)}
                  onCheckedChange={(checked) => onSelectLink(link.id, !!checked)}
                  aria-label={`Select ${link.campaign}`}
                />
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <img
                    src={link.campaignLogo || "/placeholder.svg"}
                    alt={link.campaign}
                    className="h-8 w-8 rounded-full object-cover"
                  />
                  <span className="font-medium">{link.campaign}</span>
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <span className="max-w-[200px] truncate">{link.link}</span>
                  <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => onCopyLink(link.link)}>
                    <CopyIcon className="h-3 w-3" />
                    <span className="sr-only">Copy link</span>
                  </Button>
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-1">
                  <span className="font-medium">{link.clicks.toLocaleString()}</span>
                  <ChangeIndicator value={link.clicksChange} />
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-1">
                  <span className="font-medium">${link.earnings.toFixed(2)}</span>
                  <ChangeIndicator value={link.earningsChange} />
                </div>
              </TableCell>
              <TableCell>
                <div className="w-[100px]">
                  <LinkPerformanceChart
                    data={link.performance}
                    linkId={link.id}
                    dataKey="clicks"
                    height={40}
                  />
                </div>
              </TableCell>
              <TableCell>
                <StatusBadge status={link.status} />
              </TableCell>
              <TableCell className="text-right">
                <div className="flex items-center justify-end gap-2">
                  <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => onViewAnalytics(link.id)}>
                    <BarChart3Icon className="h-4 w-4" />
                    <span className="sr-only">View Analytics</span>
                  </Button>
                  <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => onGenerateQR(link)}>
                    <QrCodeIcon className="h-4 w-4" />
                    <span className="sr-only">Generate QR Code</span>
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontalIcon className="h-4 w-4" />
                        <span className="sr-only">Actions</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => onViewAnalytics(link.id)}>
                        <BarChart3Icon className="mr-2 h-4 w-4" />
                        View Analytics
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {link.status !== "active" && (
                        <DropdownMenuItem onClick={() => onStatusChange(link.id, "active")}>
                          <PlayIcon className="mr-2 h-4 w-4" />
                          Activate
                        </DropdownMenuItem>
                      )}
                      {link.status !== "paused" && (
                        <DropdownMenuItem onClick={() => onStatusChange(link.id, "paused")}>
                          <PauseIcon className="mr-2 h-4 w-4" />
                          Pause
                        </DropdownMenuItem>
                      )}
                      {link.status !== "completed" && (
                        <DropdownMenuItem onClick={() => onStatusChange(link.id, "completed")}>
                          <CheckIcon className="mr-2 h-4 w-4" />
                          Mark as Completed
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => onCopyLink(link.link)}>
                        <CopyIcon className="mr-2 h-4 w-4" />
                        Copy Link
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onGenerateQR(link)}>
                        <QrCodeIcon className="mr-2 h-4 w-4" />
                        Generate QR Code
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
})

AffiliateLinkTableView.displayName = 'AffiliateLinkTableView'
