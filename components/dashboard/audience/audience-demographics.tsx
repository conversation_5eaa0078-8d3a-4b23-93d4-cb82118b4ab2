"use client"

import { <PERSON>, Card<PERSON>ontent, CardDes<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Suspense, memo, useMemo, lazy } from "react"
import { OptimizedChartSkeleton, OptimizedChart, convertToOptimizedFormat } from "@/lib/chart-provider-optimized"

// Optimized data with reduced memory footprint
const ageData = [
  { name: "18-24", value: 25, count: 31223 },
  { name: "25-34", value: 35, count: 43712 },
  { name: "35-44", value: 20, count: 24978 },
  { name: "45-54", value: 12, count: 14987 },
  { name: "55+", value: 8, count: 9992 },
]

const genderData = [
  { name: "Female", value: 52, count: 64984 },
  { name: "Male", value: 46, count: 57436 },
  { name: "Other", value: 2, count: 2498 },
]

const locationData = [
  { name: "United States", value: 35, count: 43712 },
  { name: "United Kingdom", value: 18, count: 22486 },
  { name: "Canada", value: 12, count: 14987 },
  { name: "Australia", value: 8, count: 9992 },
  { name: "Germany", value: 7, count: 8743 },
  { name: "Other", value: 20, count: 24978 },
]

// Lazy-loaded chart components with optimized rendering
const DemographicChart = lazy(() => 
  Promise.resolve({
    default: memo(function DemographicChart({
      data,
      type = "bar"
    }: {
      data: any[]
      type?: "bar" | "area"
    }) {
      const chartData = useMemo(() => {
        return {
          labels: data.map(item => item.name),
          datasets: [{
            label: 'Percentage',
            data: data.map(item => item.value),
            backgroundColor: `hsl(${Math.random() * 360}, 70%, 50%)`,
            borderColor: `hsl(${Math.random() * 360}, 70%, 40%)`,
            fill: type === "area"
          }]
        }
      }, [data, type])
      
      return (
        <OptimizedChart
          type={type}
          data={chartData}
          height={300}
          className="w-full"
        />
      )
    })
  })
)

// Main optimized component
export function AudienceDemographics() {
  // Memoize static data arrays
  const memoizedAgeData = useMemo(() => ageData, [])
  const memoizedGenderData = useMemo(() => genderData, [])
  const memoizedLocationData = useMemo(() => locationData, [])

  // Memoize summary statistics
  const summaryStats = useMemo(() => {
    const totalAudience = memoizedAgeData.reduce((sum, item) => sum + item.count, 0)
    const dominantAge = memoizedAgeData.reduce((max, current) => 
      current.value > max.value ? current : max
    )
    const dominantGender = memoizedGenderData.reduce((max, current) => 
      current.value > max.value ? current : max
    )
    const topLocation = memoizedLocationData.reduce((max, current) => 
      current.value > max.value ? current : max
    )

    return {
      totalAudience,
      dominantAge: dominantAge.name,
      dominantGender: dominantGender.name,
      topLocation: topLocation.name
    }
  }, [memoizedAgeData, memoizedGenderData, memoizedLocationData])

  return (
    <Card>
      <CardHeader>
        <CardTitle>Audience Demographics</CardTitle>
        <CardDescription>Breakdown by age, gender, and location</CardDescription>
        <div className="mt-2 text-sm text-muted-foreground">
          Total: {summaryStats.totalAudience.toLocaleString()} • 
          Top Age: {summaryStats.dominantAge} • 
          Top Gender: {summaryStats.dominantGender} • 
          Top Location: {summaryStats.topLocation}
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="age">
          <TabsList className="mb-4">
            <TabsTrigger value="age">Age</TabsTrigger>
            <TabsTrigger value="gender">Gender</TabsTrigger>
            <TabsTrigger value="location">Location</TabsTrigger>
          </TabsList>
          <TabsContent value="age">
            <Suspense fallback={<OptimizedChartSkeleton height={300} />}>
              <DemographicChart data={memoizedAgeData} type="bar" />
            </Suspense>
          </TabsContent>
          <TabsContent value="gender">
            <Suspense fallback={<OptimizedChartSkeleton height={300} />}>
              <DemographicChart data={memoizedGenderData} type="bar" />
            </Suspense>
          </TabsContent>
          <TabsContent value="location">
            <Suspense fallback={<OptimizedChartSkeleton height={300} />}>
              <DemographicChart data={memoizedLocationData} type="bar" />
            </Suspense>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
