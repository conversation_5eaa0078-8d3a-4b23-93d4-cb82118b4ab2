"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Suspense, memo, useMemo } from "react"
import { ChartProvider, RechartsComponents, ChartSkeleton } from "@/lib/chart-provider"
import { ChartTooltip } from "@/components/ui/chart"

const ageData = [
  { name: "18-24", value: 25, count: 31223 },
  { name: "25-34", value: 35, count: 43712 },
  { name: "35-44", value: 20, count: 24978 },
  { name: "45-54", value: 12, count: 14987 },
  { name: "55+", value: 8, count: 9992 },
]

const genderData = [
  { name: "Female", value: 52, count: 64944 },
  { name: "Male", value: 45, count: 56202 },
  { name: "Other", value: 3, count: 3746 },
]

const locationData = [
  { name: "United States", value: 40, count: 49957 },
  { name: "United Kingdom", value: 15, count: 18734 },
  { name: "Canada", value: 12, count: 14987 },
  { name: "Australia", value: 8, count: 9992 },
  { name: "Germany", value: 6, count: 7494 },
  { name: "Other", value: 19, count: 23728 },
]

const COLORS = ["#8884d8", "#82ca9d", "#ffc658", "#ff8042", "#0088fe", "#00C49F"]

// Optimized chart components using centralized provider
const DemographicPieChart = memo(function DemographicPieChart({
  data,
  colors = COLORS
}: {
  data: any[],
  colors?: string[]
}) {
  return (
    <ChartProvider>
      <RechartsComponents>
        {(components: any) => (
          <components.ResponsiveContainer width="100%" height={300}>
            <components.PieChart>
              <components.Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={100}
                fill="#8884d8"
                dataKey="value"
                label={({ name, percent }: any) => `${name}: ${(percent * 100).toFixed(0)}%`}
              >
                {data.map((entry: any, index: number) => (
                  <components.Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                ))}
              </components.Pie>
              <components.Tooltip content={<ChartTooltip />} />
              <components.Legend />
            </components.PieChart>
          </components.ResponsiveContainer>
        )}
      </RechartsComponents>
    </ChartProvider>
  )
})

const DemographicBarChart = memo(function DemographicBarChart({
  data,
  fill = "#8884d8"
}: {
  data: any[],
  fill?: string
}) {
  return (
    <ChartProvider>
      <RechartsComponents>
        {(components: any) => (
          <components.ResponsiveContainer width="100%" height={300}>
            <components.BarChart data={data}>
              <components.CartesianGrid strokeDasharray="3 3" />
              <components.XAxis dataKey="name" />
              <components.YAxis />
              <components.Tooltip content={<ChartTooltip />} />
              <components.Bar dataKey="count" fill={fill} radius={[4, 4, 0, 0]} />
            </components.BarChart>
          </components.ResponsiveContainer>
        )}
      </RechartsComponents>
    </ChartProvider>
  )
})

export function AudienceDemographics() {
  // Memoize static data arrays
  const memoizedAgeData = useMemo(() => ageData, [])
  const memoizedGenderData = useMemo(() => genderData, [])
  const memoizedLocationData = useMemo(() => locationData, [])

  return (
    <Card>
      <CardHeader>
        <CardTitle>Audience Demographics</CardTitle>
        <CardDescription>Breakdown by age, gender, and location</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="age">
          <TabsList className="mb-4">
            <TabsTrigger value="age">Age</TabsTrigger>
            <TabsTrigger value="gender">Gender</TabsTrigger>
            <TabsTrigger value="location">Location</TabsTrigger>
          </TabsList>
          <TabsContent value="age">
            <Suspense fallback={<ChartSkeleton />}>
              <DemographicPieChart data={memoizedAgeData} />
            </Suspense>
            <Suspense fallback={<ChartSkeleton />}>
              <DemographicBarChart data={memoizedAgeData} fill="#8884d8" />
            </Suspense>
          </TabsContent>
          <TabsContent value="gender">
            <Suspense fallback={<ChartSkeleton />}>
              <DemographicPieChart data={memoizedGenderData} />
            </Suspense>
            <Suspense fallback={<ChartSkeleton />}>
              <DemographicBarChart data={memoizedGenderData} fill="#82ca9d" />
            </Suspense>
          </TabsContent>
          <TabsContent value="location">
            <Suspense fallback={<ChartSkeleton />}>
              <DemographicPieChart data={memoizedLocationData} />
            </Suspense>
            <Suspense fallback={<ChartSkeleton />}>
              <DemographicBarChart data={memoizedLocationData} fill="#ffc658" />
            </Suspense>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
