"use client"

import { memo, Suspense, useMemo, lazy } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { OptimizedChartSkeleton, OptimizedChart, convertToOptimizedFormat } from "@/lib/chart-provider-optimized"

// Optimized data with reduced memory footprint
const hourlyData = [
  { hour: "00", activity: 12 }, { hour: "01", activity: 8 }, { hour: "02", activity: 5 },
  { hour: "03", activity: 3 }, { hour: "04", activity: 4 }, { hour: "05", activity: 8 },
  { hour: "06", activity: 15 }, { hour: "07", activity: 25 }, { hour: "08", activity: 35 },
  { hour: "09", activity: 45 }, { hour: "10", activity: 52 }, { hour: "11", activity: 58 },
  { hour: "12", activity: 65 }, { hour: "13", activity: 72 }, { hour: "14", activity: 78 },
  { hour: "15", activity: 82 }, { hour: "16", activity: 75 }, { hour: "17", activity: 68 },
  { hour: "18", activity: 62 }, { hour: "19", activity: 58 }, { hour: "20", activity: 52 },
  { hour: "21", activity: 45 }, { hour: "22", activity: 35 }, { hour: "23", activity: 22 },
]

const dailyData = [
  { day: "Mon", activity: 85 }, { day: "Tue", activity: 92 }, { day: "Wed", activity: 78 },
  { day: "Thu", activity: 88 }, { day: "Fri", activity: 95 }, { day: "Sat", activity: 72 },
  { day: "Sun", activity: 68 },
]

// Lazy-loaded chart components
const HourlyActivityChart = lazy(() => 
  Promise.resolve({
    default: memo(function HourlyActivityChart({ data }: { data: any[] }) {
      const chartData = useMemo(() => {
        const converted = convertToOptimizedFormat(data)
        return {
          labels: converted.labels,
          datasets: [{
            label: 'Activity',
            data: converted.datasets[0]?.data || [],
            borderColor: '#8884d8',
            backgroundColor: 'rgba(136, 132, 216, 0.3)',
            fill: true
          }]
        }
      }, [data])
      
      return (
        <OptimizedChart
          type="area"
          data={chartData}
          height={300}
          className="w-full"
        />
      )
    })
  })
)

const DailyActivityChart = lazy(() => 
  Promise.resolve({
    default: memo(function DailyActivityChart({ data }: { data: any[] }) {
      const chartData = useMemo(() => {
        const converted = convertToOptimizedFormat(data)
        return {
          labels: converted.labels,
          datasets: [{
            label: 'Activity',
            data: converted.datasets[0]?.data || [],
            backgroundColor: '#82ca9d',
            borderColor: '#82ca9d',
            fill: false
          }]
        }
      }, [data])
      
      return (
        <OptimizedChart
          type="bar"
          data={chartData}
          height={300}
          className="w-full"
        />
      )
    })
  })
)

// Main optimized component
export const AudienceActivity = memo(function AudienceActivity() {
  // Memoize static data arrays
  const memoizedHourlyData = useMemo(() => hourlyData, [])
  const memoizedDailyData = useMemo(() => dailyData, [])

  // Memoize peak activity calculations
  const peakHour = useMemo(() => {
    return memoizedHourlyData.reduce((max, current) => 
      current.activity > max.activity ? current : max
    )
  }, [memoizedHourlyData])

  const peakDay = useMemo(() => {
    return memoizedDailyData.reduce((max, current) => 
      current.activity > max.activity ? current : max
    )
  }, [memoizedDailyData])

  return (
    <Card>
      <CardHeader>
        <CardTitle>Audience Activity</CardTitle>
        <CardDescription>When your audience is most active</CardDescription>
        <div className="mt-2 text-sm text-muted-foreground">
          Peak Hour: {peakHour.hour}:00 • Peak Day: {peakDay.day}
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="hourly">
          <TabsList className="mb-4">
            <TabsTrigger value="hourly">Hourly</TabsTrigger>
            <TabsTrigger value="daily">Daily</TabsTrigger>
          </TabsList>
          <TabsContent value="hourly">
            <Suspense fallback={<OptimizedChartSkeleton height={300} />}>
              <HourlyActivityChart data={memoizedHourlyData} />
            </Suspense>
          </TabsContent>
          <TabsContent value="daily">
            <Suspense fallback={<OptimizedChartSkeleton height={300} />}>
              <DailyActivityChart data={memoizedDailyData} />
            </Suspense>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
})

AudienceActivity.displayName = 'AudienceActivity'
