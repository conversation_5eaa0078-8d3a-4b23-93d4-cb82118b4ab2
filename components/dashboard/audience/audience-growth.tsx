"use client"

import { useState, memo, Suspense, useMemo, use<PERSON><PERSON>back, lazy } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { OptimizedChartSkeleton, OptimizedChart, convertToOptimizedFormat } from "@/lib/chart-provider-optimized"

// Memoized growth data with reduced memory footprint
const growthData = [
  { date: "Jan", total: 12450, new: 1250, net: 1180 },
  { date: "Feb", total: 13680, new: 1380, net: 1230 },
  { date: "Mar", total: 15120, new: 1520, net: 1440 },
  { date: "Apr", total: 16890, new: 1890, net: 1770 },
  { date: "May", total: 18950, new: 2150, net: 2060 },
  { date: "Jun", total: 21340, new: 2490, net: 2390 },
]

// Lazy-loaded chart components for better code splitting
const TotalGrowthChart = lazy(() => 
  Promise.resolve({
    default: memo(function TotalGrowthChart({ data }: { data: any[] }) {
      const chartData = useMemo(() => convertToOptimizedFormat(data), [data])
      
      return (
        <OptimizedChart
          type="area"
          data={chartData}
          height={300}
          className="w-full"
        />
      )
    })
  })
)

const NewFollowersChart = lazy(() => 
  Promise.resolve({
    default: memo(function NewFollowersChart({ data }: { data: any[] }) {
      const chartData = useMemo(() => {
        const converted = convertToOptimizedFormat(data)
        // Filter to only show 'new' data
        return {
          ...converted,
          datasets: converted.datasets.filter(d => d.label === 'new')
        }
      }, [data])
      
      return (
        <OptimizedChart
          type="bar"
          data={chartData}
          height={300}
          className="w-full"
        />
      )
    })
  })
)

const NetGrowthChart = lazy(() => 
  Promise.resolve({
    default: memo(function NetGrowthChart({ data }: { data: any[] }) {
      const chartData = useMemo(() => {
        const converted = convertToOptimizedFormat(data)
        // Filter to only show 'net' data
        return {
          ...converted,
          datasets: converted.datasets.filter(d => d.label === 'net')
        }
      }, [data])
      
      return (
        <OptimizedChart
          type="line"
          data={chartData}
          height={300}
          className="w-full"
        />
      )
    })
  })
)

// Main component with Firefox-specific optimizations
export const AudienceGrowth = memo(function AudienceGrowth() {
  // Memoize growthData to avoid recreating the array on every render
  const memoizedGrowthData = useMemo(() => growthData, [])
  const [timeframe, setTimeframeState] = useState("6months")
  const [activeTab, setActiveTabState] = useState("total")

  // Stabilize handlers with useCallback
  const setTimeframe = useCallback((v: string) => setTimeframeState(v), [])
  const setActiveTab = useCallback((v: string) => setActiveTabState(v), [])

  // Memoize filtered data based on timeframe
  const filteredData = useMemo(() => {
    switch (timeframe) {
      case "3months":
        return memoizedGrowthData.slice(-3)
      case "1year":
        return memoizedGrowthData // All data represents 1 year
      default:
        return memoizedGrowthData.slice(-6) // 6 months
    }
  }, [memoizedGrowthData, timeframe])

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Audience Growth</CardTitle>
            <CardDescription>Track follower growth over time</CardDescription>
          </div>
          <Select value={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="3months">Last 3 Months</SelectItem>
              <SelectItem value="6months">Last 6 Months</SelectItem>
              <SelectItem value="1year">Last Year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="total">Total Growth</TabsTrigger>
            <TabsTrigger value="new">New Followers</TabsTrigger>
            <TabsTrigger value="net">Net Growth</TabsTrigger>
          </TabsList>
          <TabsContent value="total">
            <Suspense fallback={<OptimizedChartSkeleton height={300} />}>
              <TotalGrowthChart data={filteredData} />
            </Suspense>
          </TabsContent>
          <TabsContent value="new">
            <Suspense fallback={<OptimizedChartSkeleton height={300} />}>
              <NewFollowersChart data={filteredData} />
            </Suspense>
          </TabsContent>
          <TabsContent value="net">
            <Suspense fallback={<OptimizedChartSkeleton height={300} />}>
              <NetGrowthChart data={filteredData} />
            </Suspense>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
})

AudienceGrowth.displayName = 'AudienceGrowth'
